#!/bin/bash
# 开发环境启动脚本

# 导入环境变量
source env.sh
# 激活虚拟环境
source venv/bin/activate
# 创建迁移文件
python manage.py makemigrations
# 执行迁移
python manage.py migrate
# 启动开发服务器
python manage.py runserver 0.0.0.0:7777



celery -A CareCenter worker --loglevel=info --pool=solo --queues=default,audit_log

nohup celery -A LFBack worker -l info > celery_worker.log 2>&1 &

nohup celery -A CareCenter beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler > celery_beat.log 2>&1 &