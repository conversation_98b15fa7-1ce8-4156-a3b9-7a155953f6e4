from django.urls import path

from .views import (
    MaternityCenterBasicInfoView,
    MaternityCenterBasicInfoUpdateView,
    MaternityCenterBrandIntroductionCreateView,
    MaternityCenterBrandIntroductionUpdateView,
    MaternityCenterCarouselListView,
    MaternityCenterCarouselUploadView,
    MaternityCenterCarouselDeleteView,
    MaternityCenterContactUsUpdateView,
    MaternityCenterContactUsCreateView,
    MaternityCenterContactUsDetailView,
    MaternityCenterBrandIntroductionView,
    MaternityCenterDevelopmentHistoryCreateView,
    MaternityCenterDevelopmentHistoryListView,
    MaternityCenterDevelopmentHistoryUpdateView,
    MaternityCenterDevelopmentHistoryDeleteView,
    MaternityCenterListView,
    HealthKnowledgeListView,
    HealthKnowledgeDetailView,
    HealthKnowledgeCreateView,
    HealthKnowledgeUpdateView,
    HealthKnowledgeDeleteView
)

urlpatterns = [
    # 需要权限的接口
    # 基本信息
    path('basic/info/', MaternityCenterBasicInfoView.as_view(), name='basic-info-list'),
    # 更新基本信息
    path('basic/info/update/', MaternityCenterBasicInfoUpdateView.as_view(), name='basic-info-update'),
    
    # 轮播图列表
    path('carousel/list/', MaternityCenterCarouselListView.as_view(), name='carousel-list'),
    # 新增轮播图
    path('carousel/upload/', MaternityCenterCarouselUploadView.as_view(), name='carousel-upload'),
    # 删除轮播图
    path('carousel/delete/<str:rid>/', MaternityCenterCarouselDeleteView.as_view(), name='carousel-delete'),
    
    # 联系我们详情
    path('contact/us/detail/', MaternityCenterContactUsDetailView.as_view(), name='contact-us-detail'),
    # 创建联系我们
    path('contact/us/create/', MaternityCenterContactUsCreateView.as_view(), name='contact-us-create'),
    # 更新联系我们
    path('contact/us/update/', MaternityCenterContactUsUpdateView.as_view(), name='contact-us-update'),
    
    
    # 品牌介绍
    path('brand/introduction/', MaternityCenterBrandIntroductionView.as_view(), name='brand-introduction'),
    # 创建品牌介绍
    path('brand/introduction/create/', MaternityCenterBrandIntroductionCreateView.as_view(), name='brand-introduction-create'),
    # 更新品牌介绍
    path('brand/introduction/update/', MaternityCenterBrandIntroductionUpdateView.as_view(), name='brand-introduction-update'),
    
    # 发展历程列表
    path('development/history/list/', MaternityCenterDevelopmentHistoryListView.as_view(), name='development-history-list'),
    # 发展历程创建
    path('development/history/create/', MaternityCenterDevelopmentHistoryCreateView.as_view(), name='development-history-create'),
    # 更新发展历程
    path('development/history/update/<str:rid>/', MaternityCenterDevelopmentHistoryUpdateView.as_view(), name='development-history-update'),
    # 删除发展历程
    path('development/history/delete/<str:rid>/', MaternityCenterDevelopmentHistoryDeleteView.as_view(), name='development-history-delete'),

    # 健康知识列表
    path('health/knowledge/list/', HealthKnowledgeListView.as_view(), name='health-knowledge-list'),
    # 健康知识详情
    path('health/knowledge/detail/<str:rid>/', HealthKnowledgeDetailView.as_view(), name='health-knowledge-detail'),
    # 创建健康知识
    path('health/knowledge/create/', HealthKnowledgeCreateView.as_view(), name='health-knowledge-create'),
    # 更新健康知识
    path('health/knowledge/update/<str:rid>/', HealthKnowledgeUpdateView.as_view(), name='health-knowledge-update'),
    # 删除健康知识
    path('health/knowledge/delete/<str:rid>/', HealthKnowledgeDeleteView.as_view(), name='health-knowledge-delete'),


    #--------------------------------非会员小程序--------------------------------
    

    # 月子中心列表
    path('list/', MaternityCenterListView.as_view(), name='maternity-center-list'),
    
    
    
    
    #--------------------------------非会员小程序--------------------------------
    # # 添加轮播图接口
    # path('carousel/add/', MaternityCenterCarouselAddView.as_view(), name='-carousel-add'),
    # # 删除轮播图接口
    # path('carousel/delete/<int:pk>/', MaternityCenterCarouselDeleteView.as_view(), name='-carousel-delete'),
] 